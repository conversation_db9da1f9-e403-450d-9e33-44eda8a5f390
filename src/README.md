# 富途 OpenAPI
## 简介

- [Futu OpenAPI](https://www.futunn.com/OpenAPI)，为您的程序化交易，提供丰富的行情和交易接口，满足每一位开发者的量化投资需求，助力您的宽客梦想。  
- [Futu OpenAPI 文档](https://openapi.futunn.com/futu-api-doc/intro/intro.html)

## 安装
- 初次安装：  
`npm install --save futu-api`
- 升级到最新版本：  
    1. 将 package.json 依赖的 futu-api 版本号改成 `*`
    2. 执行：`npm update futu-api`
- 升级或降级到指定版本：  
`npm install --save futu-api@5.3.1508`（其中的 5.3.1508 需要替换成实际的版本号）

## 目录结构
```ProtoBuf
+---Sample               示例工程    
+---src                  源码
```

## 交流与答疑

- 富途 OpenAPI 官方交流 QQ 群如下，研发工程师每天会在群里解答问题：  
    - Futu OpenAPI Ⅰ：108534288  
    - Futu OpenAPI Ⅱ：229850364   

## 提示
- 自 2021 年 4 月开始，本页面将由富途官方持续维护。官方的 futu-api，可能与原工程有差异，请知悉。
- 为保证整体的产品一致性，futu-api 的版本号将使用富途自定的版本号规则，与 npm 的版本号规则存在差异。
