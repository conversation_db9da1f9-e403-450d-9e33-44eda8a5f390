syntax = "proto2";
package Qot_GetSubInfo;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetsubinfo";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	optional bool isReqAllConn = 1; //是否返回所有连接的订阅状态,不传或者传false只返回当前连接数据
}

message S2C
{
	repeated Qot_Common.ConnSubInfo connSubInfoList = 1; //订阅订阅信息
	required int32 totalUsedQuota = 2; //FutuOpenD已使用的订阅额度
	required int32 remainQuota = 3; //FutuOpenD剩余订阅额度
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
