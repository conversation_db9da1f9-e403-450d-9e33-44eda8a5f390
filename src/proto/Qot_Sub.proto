syntax = "proto2";
package Qot_Sub;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotsub";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	repeated Qot_Common.Security securityList = 1; //股票
	repeated int32 subTypeList = 2; //Qot_Common.SubType,订阅数据类型
	required bool isSubOrUnSub = 3; //ture表示订阅,false表示反订阅
	optional bool isRegOrUnRegPush = 4; //是否注册或反注册该连接上面行情的推送,该参数不指定不做注册反注册操作
	repeated int32 regPushRehabTypeList = 5; //Qot_Common.RehabType,复权类型,注册推送并且是K线类型才生效,其他订阅类型忽略该参数,注册K线推送时该参数不指定默认前复权
	optional bool isFirstPush = 6; //注册后如果本地已有数据是否首推一次已存在数据,该参数不指定则默认true
	optional bool isUnsubAll = 7; //当被设置为True时忽略其他参数，取消当前连接的所有订阅，并且反注册推送。
	optional bool isSubOrderBookDetail = 8; //订阅摆盘可用,是否订阅摆盘明细,仅支持SF行情,该参数不指定则默认false
	optional bool extendedTime = 9;  // 是否允许美股盘前盘后数据（仅用于订阅美股的实时K线、实时分时、实时逐笔）
	optional int32 session = 10; // 时段
}

message S2C
{
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
			
	optional S2C s2c = 4;
}
