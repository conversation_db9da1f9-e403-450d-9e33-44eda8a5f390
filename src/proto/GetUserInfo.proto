syntax = "proto2";
package GetUserInfo;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/getuserinfo";

import "Common.proto";

enum UpdateType
{
	UpdateType_None = 0; //无需升级
	UpdateType_Advice = 1; //建议升级
	UpdateType_Force = 2; //强制升级
}

enum UserInfoField
{
	UserInfoField_Basic = 1; //昵称，用户头像，牛牛号
	UserInfoField_API = 2; //API权限信息
	UserInfoField_QotRight = 4; //市场的行情权限
	UserInfoField_Disclaimer = 8; //免责
	UserInfoField_Update = 16; //升级类型

	UserInfoField_WebKey = 2048;
}

message C2S
{
	optional int32 flag = 2; //UserInfoField集合，不设置默认返回全部信息
}

message S2C
{
	optional string nickName = 1; //用户昵称
	optional string avatarUrl = 2; //用户头像url
	optional string apiLevel = 3; //api用户等级描述, 已在2.10版本之后废弃
	optional int32 hkQotRight = 4; //港股行情权限, QotRight
	optional int32 usQotRight = 5; //美股行情权限, QotRight
	optional int32 cnQotRight = 6; //A股行情权限, QotRight // 废弃，使用shQotRight和szQotRight
	optional bool isNeedAgreeDisclaimer = 7; //已开户用户需要同意免责声明，未开户或已同意的用户返回false
	optional int64 userID = 8; //用户牛牛号
	optional int32 updateType = 9; //升级类型，UpdateType
	optional string webKey = 10; 
	optional string webJumpUrlHead = 18; 
	optional int32 hkOptionQotRight = 11; //港股期权行情权限, Qot_Common.QotRight
	optional bool hasUSOptionQotRight = 12; //是否有美股期权行情权限
	optional int32 hkFutureQotRight = 13; //港股期货行情权限, Qot_Common.QotRight
	optional int32 subQuota = 14; //订阅额度
    optional int32 historyKLQuota = 15; //历史K线额度
	optional int32 usFutureQotRight = 16; //美股期货行情权限, Qot_Common.QotRight(已废弃)
	optional int32 usOptionQotRight = 17; //美股期货行情权限, Qot_Common.QotRight
	optional int32 userAttribution = 19; //用户注册归属地, Common.UserAttribution
	optional string updateWhatsNew = 20; //升级提示
	optional int32 usIndexQotRight = 21; //美股指数行情权限, Qot_Common.QotRight
	optional int32 usOtcQotRight = 22; //美股OTC市场行情权限, Qot_Common.QotRight
	optional int32 usCMEFutureQotRight = 23; //美股CME期货行情权限, Qot_Common.QotRight
	optional int32 usCBOTFutureQotRight = 24; //美股CBOT期货行情权限, Qot_Common.QotRight
	optional int32 usNYMEXFutureQotRight = 25; //美股NYMEX期货行情权限, Qot_Common.QotRight
	optional int32 usCOMEXFutureQotRight = 26; //美股COMEX期货行情权限, Qot_Common.QotRight
	optional int32 usCBOEFutureQotRight = 27; //美股CBOE期货行情权限, Qot_Common.QotRight
	optional int32 sgFutureQotRight = 28; //新加坡市场期货行情权限, Qot_Common.QotRight
	optional int32 jpFutureQotRight = 29; //日本市场期货行情权限, Qot_Common.QotRight
	optional bool isAppNNOrMM = 30; //true:NN false:MM
	optional int32 shQotRight = 31; //上海市场行情权限, Qot_Common.QotRight
	optional int32 szQotRight = 32; //深圳市场行情权限, Qot_Common.QotRight
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //返回结果，参见Common.RetType的枚举定义
	optional string retMsg = 2; //返回结果描述
	optional int32 errCode = 3; //错误码，客户端一般通过retType和retMsg来判断结果和详情，errCode只做日志记录，仅在个别协议失败时对账用
	
	optional S2C s2c = 4;
}
