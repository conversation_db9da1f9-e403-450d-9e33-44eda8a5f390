syntax = "proto2";
package Qot_GetBasicQot;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetbasicqot";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	repeated Qot_Common.Security securityList = 1; //股票
}

message S2C
{
	repeated Qot_Common.BasicQot basicQotList = 1; //股票基本行情
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
