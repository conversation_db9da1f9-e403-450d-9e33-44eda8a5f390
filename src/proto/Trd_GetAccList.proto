syntax = "proto2";
package Trd_GetAccList;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdgetacclist";

import "Common.proto";
import "Trd_Common.proto";

message C2S
{
	required uint64 userID = 1; //历史原因，目前已废弃，填0即可
	optional int32 trdCategory = 2; //交易品类，参考 Trd_Common.TrdCategory
	optional bool needGeneralSecAccount = 3; //是否返回全能账户，仅SG用户需要
}

message S2C
{
	repeated Trd_Common.TrdAcc accList = 1; //交易业务账户列表
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	//以下3个字段每条协议都有，注释说明在InitConnect.proto中
	required int32 retType = 1 [default = -400];
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
