syntax = "proto2";
package Qot_GetFutureInfo;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetfutureinfo";

import "Common.proto";
import "Qot_Common.proto";

//交易时间
message TradeTime
{
    optional double begin = 1; // 开始时间,以分钟为单位
	optional double end = 2; // 结束时间,以分钟为单位
}

//期货合约资料的列表
message FutureInfo
{
	required string name = 1; // 合约名称
	required Qot_Common.Security security = 2; // 合约代码
	required string lastTradeTime = 3; //最后交易日，只有非主连期货合约才有该字段
	optional double lastTradeTimestamp = 4; //最后交易日时间戳，只有非主连期货合约才有该字段
	optional Qot_Common.Security owner = 5; //标的股 股票期货和股指期货才有该字段
	required string ownerOther = 6; //标的 
	required string exchange = 7; //交易所
	required string contractType = 8; //合约类型
	required double contractSize = 9; //合约规模
	required string contractSizeUnit = 10; //合约规模的单位
	required string quoteCurrency = 11; //报价货币
	required double minVar = 12; //最小变动单位
	required string minVarUnit = 13; //最小变动单位的单位
	optional string quoteUnit = 14; //报价单位
	repeated TradeTime tradeTime = 15; //交易时间
	required string timeZone = 16; //所在时区
	required string exchangeFormatUrl = 17; //交易所规格
	optional Qot_Common.Security origin = 18; //实际合约代码
}

message C2S
{
	repeated Qot_Common.Security securityList = 1; //股票列表
}

message S2C
{
	repeated FutureInfo futureInfoList = 1; //期货合约资料的列表
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
