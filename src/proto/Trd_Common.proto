syntax = "proto2";
package Trd_Common;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdcommon";

import "Common.proto";

//交易环境
enum TrdEnv
{
	TrdEnv_Simulate = 0; //仿真环境(模拟环境)
	TrdEnv_Real = 1; //真实环境
}

//交易品类
enum TrdCategory
{
	TrdCategory_Unknown = 0; //未知品类
	TrdCategory_Security = 1; //证券
	TrdCategory_Future = 2; //期货
}

//交易市场，是大的市场，不是具体品种
enum TrdMarket
{
	TrdMarket_Unknown = 0; //未知市场
	TrdMarket_HK = 1; //香港市场
	TrdMarket_US = 2; //美国市场
	TrdMarket_CN = 3; //大陆市场
	TrdMarket_HKCC = 4; //香港A股通市场
	TrdMarket_Futures = 5; //期货市场
	TrdMarket_SG = 6; //期货市场
    TrdMarket_AU = 8; //澳洲市场
    TrdMarket_Futures_Simulate_HK = 10; // 模拟交易期货市场
    TrdMarket_Futures_Simulate_US = 11;
    TrdMarket_Futures_Simulate_SG = 12;
    TrdMarket_Futures_Simulate_JP = 13;
    TrdMarket_JP = 15;
    TrdMarket_MY = 111;
    TrdMarket_CA = 112;
	TrdMarket_HK_Fund = 113; //香港基金市场
	TrdMarket_US_Fund = 123; //美国基金市场
}

//可交易证券所属市场，目前主要是区分A股的沪市和深市，香港和美国暂不需要细分
enum TrdSecMarket
{
	TrdSecMarket_Unknown = 0; //未知市场
	TrdSecMarket_HK = 1; //香港市场(股票、窝轮、牛熊、期权、期货等)
	TrdSecMarket_US = 2; //美国市场(股票、期权、期货等)
	TrdSecMarket_CN_SH = 31; //沪股市场(股票)
	TrdSecMarket_CN_SZ = 32; //深股市场(股票)
	TrdSecMarket_SG = 41;    //新加坡市场(期货)
	TrdSecMarket_JP = 51;    //日本市场(期货)
    TrdSecMarket_AU = 61; // 澳大利亚
    TrdSecMarket_MY = 71; // 马来西亚
    TrdSecMarket_CA = 81; // 加拿大
    TrdSecMarket_FX = 91; // 外汇
}

//交易方向
enum TrdSide
{
	//客户端下单只传Buy或Sell即可，SellShort是美股订单时服务器返回有此方向，BuyBack目前不存在，但也不排除服务器会传
	TrdSide_Unknown = 0; //未知方向
	TrdSide_Buy = 1; //买入
	TrdSide_Sell = 2; //卖出
	TrdSide_SellShort = 3; //卖空
	TrdSide_BuyBack = 4; //买回
}

//订单类型
enum OrderType
{
	OrderType_Unknown = 0; //未知类型
	OrderType_Normal = 1; //普通订单(港股的增强限价单、港股期权的限价单，A股限价委托、美股的限价单，港股期货的限价单，CME期货的限价单)。目前港股期权只能指定此订单类型。
	OrderType_Market = 2; //市价订单(目前支持美股、港股正股、涡轮、牛熊、界内证)
	
	OrderType_AbsoluteLimit = 5; //绝对限价订单(目前仅港股)，只有价格完全匹配才成交，否则下单失败，比如你下价格为5元的买单，卖单价格必须也要是5元才能成交，低于5元也不能成交，下单失败。卖出同理
	OrderType_Auction = 6; //竞价订单(目前仅港股)，仅港股早盘竞价和收盘竞价有效，A股的早盘竞价订单类型不变还是OrderType_Normal
	OrderType_AuctionLimit = 7; //竞价限价订单(目前仅港股)，仅早盘竞价和收盘竞价有效，参与竞价，且要求满足指定价格才会成交
	OrderType_SpecialLimit = 8; //特别限价订单(目前仅港股)，成交规则同增强限价订单，且部分成交后，交易所自动撤销订单
	OrderType_SpecialLimit_All = 9; //特别限价且要求全部成交订单(目前仅港股)，要么全部成交，要么自动撤单
	OrderType_Stop = 10; // 止损市价单
    OrderType_StopLimit = 11; // 止损限价单
    OrderType_MarketifTouched = 12; // 触及市价单（止盈）
    OrderType_LimitifTouched = 13; // 触及限价单（止盈）
    OrderType_TrailingStop = 14; // 跟踪止损市价单
    OrderType_TrailingStopLimit = 15; // 跟踪止损限价单
	OrderType_TWAP_MARKET = 16; // TWAP 市价单 
	OrderType_TWAP_LIMIT = 17;  // TWAP 限订单 
	OrderType_VWAP_MARKET = 18; // VWAP 市价单 
	OrderType_VWAP_LIMIT = 19;  // VWAP 限订单
}

//跟踪类型
enum TrailType
{
	TrailType_Unknown = 0; //未知类型
	TrailType_Ratio = 1; //比例
	TrailType_Amount = 2; //金额
}

//订单状态
enum OrderStatus
{
	OrderStatus_Unsubmitted = 0; //未提交
	OrderStatus_Unknown = -1; //未知状态
	OrderStatus_WaitingSubmit = 1; //等待提交
	OrderStatus_Submitting = 2; //提交中
	OrderStatus_SubmitFailed = 3; //提交失败，下单失败
	OrderStatus_TimeOut = 4; //处理超时，结果未知
	OrderStatus_Submitted = 5; //已提交，等待成交
	OrderStatus_Filled_Part = 10; //部分成交
	OrderStatus_Filled_All = 11; //全部已成
	OrderStatus_Cancelling_Part = 12; //正在撤单_部分(部分已成交，正在撤销剩余部分)
	OrderStatus_Cancelling_All = 13; //正在撤单_全部
	OrderStatus_Cancelled_Part = 14; //部分成交，剩余部分已撤单
	OrderStatus_Cancelled_All = 15; //全部已撤单，无成交
	OrderStatus_Failed = 21; //下单失败，服务拒绝
	OrderStatus_Disabled = 22; //已失效
	OrderStatus_Deleted = 23; //已删除，无成交的订单才能删除
	OrderStatus_FillCancelled = 24; //成交被撤销，一般遇不到，意思是已经成交的订单被回滚撤销，成交无效变为废单
};

//一笔成交的状态
enum OrderFillStatus
{
	OrderFillStatus_OK = 0; //正常
	OrderFillStatus_Cancelled = 1; //成交被取消
	OrderFillStatus_Changed = 2; //成交被更改
}

//持仓方向类型
enum PositionSide
{
	PositionSide_Long = 0; //多仓，默认情况是多仓
	PositionSide_Unknown = -1; //未知方向
	PositionSide_Short = 1; //空仓
};

//修改订单的操作类型
enum ModifyOrderOp
{
	//港股支持全部操作，美股目前仅支持ModifyOrderOp_Normal和ModifyOrderOp_Cancel
	ModifyOrderOp_Unknown = 0; //未知操作
	ModifyOrderOp_Normal = 1; //修改订单的价格、数量等，即以前的改单
	ModifyOrderOp_Cancel = 2; //撤单
	ModifyOrderOp_Disable = 3; //失效
	ModifyOrderOp_Enable = 4; //生效
	ModifyOrderOp_Delete = 5; //删除
};

//交易账户类型
enum TrdAccType
{
	TrdAccType_Unknown = 0; //未知类型
	TrdAccType_Cash = 1;    //现金账户
	TrdAccType_Margin = 2;  //保证金账户
};

//交易账户状态
enum TrdAccStatus
{
    TrdAccStatus_Active = 0;
    TrdAccStatus_Disabled = 1;
}

//货币种类
enum Currency
{
	Currency_Unknown = 0;  //未知货币
	Currency_HKD = 1;   // 港币
    Currency_USD = 2;   // 美元
    Currency_CNH = 3;   // 离岸人民币
	Currency_JPY = 4;   // 日元
	Currency_SGD = 5;   // 新币
	Currency_AUD = 6;   // 澳元
    Currency_CAD = 7; // 加拿大元
    Currency_MYR = 8; // 马来西亚林吉特
}

//账户风险控制等级
enum CltRiskLevel
{
	CltRiskLevel_Unknown = -1;		// 未知
    CltRiskLevel_Safe = 0;          // 安全
    CltRiskLevel_Warning = 1;       // 预警
    CltRiskLevel_Danger = 2;        // 危险
    CltRiskLevel_AbsoluteSafe = 3;  // 绝对安全
    CltRiskLevel_OptDanger = 4;     // 危险, 期权相关
}

//订单有效期
enum TimeInForce
{
	TimeInForce_DAY = 0;       // 当日有效
	TimeInForce_GTC = 1;       // 撤单前有效，最多持续90自然日。
}

//券商
enum SecurityFirm
{
    SecurityFirm_Unknown = 0;        //未知
    SecurityFirm_FutuSecurities = 1; //富途证券（香港）
	SecurityFirm_FutuInc = 2;        //富途证券（美国）
	SecurityFirm_FutuSG = 3;         //富途证券（新加坡）
	SecurityFirm_FutuAU = 4;         //富途证券（澳洲）
}

//模拟交易账户类型
enum SimAccType
{
	SimAccType_Unknown = 0;		//未知
	SimAccType_Stock = 1;		//股票模拟账户（仅用于交易证券类产品，不支持交易期权）
	SimAccType_Option = 2;      //期权模拟账户（仅用于交易期权，不支持交易股票证券类产品）
    SimAccType_Futures = 3;     //期货模拟账户
}

//风险状态，共分 9 个等级，LEVEL1是最安全，LEVEL9是最危险
enum CltRiskStatus
{
	CltRiskStatus_Unknown = 0; //未知
	CltRiskStatus_Level1 = 1;  //非常安全
	CltRiskStatus_Level2 = 2;  //安全
	CltRiskStatus_Level3 = 3;  //较安全
	CltRiskStatus_Level4 = 4;  //较低风险
	CltRiskStatus_Level5 = 5;  //中等风险
	CltRiskStatus_Level6 = 6;  //较高风险
	CltRiskStatus_Level7 = 7;  //预警
	CltRiskStatus_Level8 = 8;  //预警
	CltRiskStatus_Level9 = 9;  //预警
}

//日内交易限制情况
enum DTStatus
{
	DTStatus_Unknown = 0; 		//未知
	DTStatus_Unlimited = 1;		//无限次(当前可以无限次日内交易，注意留意剩余日内交易购买力)
	DTStatus_EMCall = 2;		//EM Call(当前状态不能新建仓位，需要补充资产净值至$25000以上，否则会被禁止新建仓位90天)
	DTStatus_DTCall = 3;		//DT Call(当前状态有未补平的日内交易追缴金额（DTCall），需要在5个交易日内足额入金来补平 DTCall，否则会被禁止新建仓位，直到足额存入资金才会解禁)
}

//账户现金信息，目前仅用于期货账户
message AccCashInfo
{
    optional int32 currency = 1;        // 货币类型，取值参考 Currency
    optional double cash = 2;           // 现金结余
    optional double availableBalance = 3;   // 现金可提金额
	optional double netCashPower = 4;		// 现金购买力
}

//分市场资产信息
message AccMarketInfo
{
    optional int32 trdMarket = 1;        // 交易市场, 参见TrdMarket的枚举定义
    optional double assets = 2;           // 分市场资产信息
}

//交易协议公共参数头
message TrdHeader
{
	required int32 trdEnv = 1; //交易环境, 参见TrdEnv的枚举定义
	required uint64 accID = 2; //业务账号, 业务账号与交易环境、市场权限需要匹配，否则会返回错误
	required int32 trdMarket = 3; //交易市场, 参见TrdMarket的枚举定义
}

//交易业务账户结构
message TrdAcc
{
	required int32 trdEnv = 1; //交易环境，参见TrdEnv的枚举定义
	required uint64 accID = 2; //业务账号
	repeated int32 trdMarketAuthList = 3; //业务账户支持的交易市场权限，即此账户能交易那些市场, 可拥有多个交易市场权限，目前仅单个，取值参见TrdMarket的枚举定义
	optional int32 accType = 4;   //账户类型，取值见TrdAccType
	optional string cardNum = 5;  //卡号
	optional int32 securityFirm = 6; //所属券商，取值见SecurityFirm
	optional int32 simAccType = 7; //模拟交易账号类型，取值见SimAccType
    optional string uniCardNum = 8;  //所属综合账户卡号
    optional int32 accStatus = 9; //账号状态，取值见TrdAccStatus
}

//账户资金结构
message Funds
{
	required double power = 1; //最大购买力（做多），3位精度，下同。
	required double totalAssets = 2; //资产净值
	required double cash = 3; //现金
	required double marketVal = 4; //证券市值, 仅证券账户适用
	required double frozenCash = 5; //冻结资金
	required double debtCash = 6; //计息金额
	required double avlWithdrawalCash = 7; //现金可提，仅证券账户适用 

	optional int32 currency = 8;            //币种，本结构体资金相关的货币类型，取值参见 Currency，期货适用
    optional double availableFunds = 9;     //可用资金，期货适用
    optional double unrealizedPL = 10;      //未实现盈亏，期货适用
    optional double realizedPL = 11;        //已实现盈亏，期货适用
    optional int32 riskLevel = 12;           //风控状态，参见 CltRiskLevel, 期货适用
    optional double initialMargin = 13;      //初始保证金
    optional double maintenanceMargin = 14;  //维持保证金
    repeated AccCashInfo cashInfoList = 15;  //分币种的现金信息，期货适用

	optional double maxPowerShort = 16; //卖空购买力
	optional double netCashPower = 17;  //现金购买力
	optional double longMv = 18;        //多头市值
	optional double shortMv = 19;       //空头市值
	optional double pendingAsset = 20;  //在途资产
	optional double maxWithdrawal = 21;          //融资可提，仅证券账户适用
	optional int32 riskStatus = 22;              //风险状态，参见 [CltRiskStatus]，证券账户适用，共分 9 个等级，LEVEL1是最安全，LEVEL9是最危险
	optional double marginCallMargin = 23;       //	Margin Call 保证金
	
	optional bool isPdt = 24;				//是否PDT账户，仅富途证券（美国）账户适用
	optional string pdtSeq = 25;			//剩余日内交易次数
	optional double beginningDTBP = 26;		//初始日内交易购买力
	optional double remainingDTBP = 27;		//剩余日内交易购买力
	optional double dtCallAmount = 28;		//日内交易待缴金额
	optional int32 dtStatus = 29;				//日内交易限制情况，取值见DTStatus
	
	optional double securitiesAssets = 30; // 证券资产净值
	optional double fundAssets = 31; // 基金资产净值
	optional double bondAssets = 32; // 债券资产净值
    
    repeated AccMarketInfo marketInfoList = 33; //分市场资产信息
}

//账户持仓结构
message Position
{
	required uint64 positionID = 1;     //持仓ID，一条持仓的唯一标识
    required int32 positionSide = 2;    //持仓方向，参见PositionSide的枚举定义
    required string code = 3;           //代码
    required string name = 4;           //名称
    required double qty = 5;            //持有数量，2位精度，期权单位是"张"，下同
    required double canSellQty = 6;     //可卖数量
    required double price = 7;          //市价，3位精度，期货为2位精度
    optional double costPrice = 8;      //成本价，无精度限制，期货为2位精度，如果没传，代表此时此值无效（已废弃，请使用 dilutedCostPrice 或 averageCostPrice）
    required double val = 9;            //市值，3位精度, 期货此字段值为0
    required double plVal = 10;         //盈亏金额，3位精度，期货为2位精度
    optional double plRatio = 11;       //摊薄成本价的盈亏百分比(如plRatio等于8.8代表涨8.8%)，无精度限制，如果没传，代表此时此值无效
    optional int32 secMarket = 12;      //证券所属市场，参见TrdSecMarket的枚举定义
    
	//以下是此持仓今日统计
    optional double td_plVal = 21;      //今日盈亏金额，3位精度，下同, 期货为2位精度
    optional double td_trdVal = 22;     //今日交易额，期货不适用
    optional double td_buyVal = 23;     //今日买入总额，期货不适用
    optional double td_buyQty = 24;     //今日买入总量，期货不适用
    optional double td_sellVal = 25;    //今日卖出总额，期货不适用
    optional double td_sellQty = 26;    //今日卖出总量，期货不适用

    optional double unrealizedPL = 28;       //未实现盈亏，期货适用
    optional double realizedPL = 29;         //已实现盈亏，期货适用
	optional int32 currency = 30;        // 货币类型，取值参考 Currency
	optional int32 trdMarket = 31;  //交易市场, 参见TrdMarket的枚举定义
	
    optional double dilutedCostPrice = 32;      //摊薄成本价，仅支持证券账户使用
    optional double averageCostPrice = 33;      //平均成本价，模拟交易证券账户不适用
    optional double averagePlRatio = 34;        //平均成本价的盈亏百分比(如plRatio等于8.8代表涨8.8%)，无精度限制，如果没传，代表此时此值无效
}

//订单结构
message Order
{
	required int32 trdSide = 1; //交易方向, 参见TrdSide的枚举定义
    required int32 orderType = 2; //订单类型, 参见OrderType的枚举定义
    required int32 orderStatus = 3; //订单状态, 参见OrderStatus的枚举定义
    required uint64 orderID = 4; //订单号
    required string orderIDEx = 5; //扩展订单号(仅查问题时备用)
    required string code = 6; //代码
    required string name = 7; //名称
    required double qty = 8; //订单数量，2位精度，期权单位是"张"
    optional double price = 9; //订单价格，3位精度
    required string createTime = 10; //创建时间，严格按YYYY-MM-DD HH:MM:SS或YYYY-MM-DD HH:MM:SS.MS格式传
    required string updateTime = 11; //最后更新时间，严格按YYYY-MM-DD HH:MM:SS或YYYY-MM-DD HH:MM:SS.MS格式传
    optional double fillQty = 12; //成交数量，2位精度，期权单位是"张"
    optional double fillAvgPrice = 13; //成交均价，无精度限制
    optional string lastErrMsg = 14; //最后的错误描述，如果有错误，会有此描述最后一次错误的原因，无错误为空
    optional int32 secMarket = 15; //证券所属市场，参见TrdSecMarket的枚举定义
    optional double createTimestamp = 16; //创建时间戳
    optional double updateTimestamp = 17; //最后更新时间戳
    optional string remark = 18; //用户备注字符串，最大长度64字节
	optional int32 timeInForce = 19; //订单期限，参考 TimeInForce 类的定义
	optional bool fillOutsideRTH = 20; //是否允许美股订单盘前盘后成交
	optional double auxPrice = 21; //触发价格
	optional int32 trailType = 22; //跟踪类型, 参见Trd_Common.TrailType的枚举定义
	optional double trailValue = 23; //跟踪金额/百分比
	optional double trailSpread = 24; //指定价差
	optional int32 currency = 25;        // 货币类型，取值参考 Currency
	optional int32 trdMarket = 26;  //交易市场, 参见TrdMarket的枚举定义
	optional int32 session = 27; //美股订单时段, 参见Common.Session的枚举定义
}

message OrderFeeItem
{
	optional string title = 1; //费用名字
	optional double value = 2; //费用金额
}

message OrderFee
{
    required string orderIDEx = 1; //扩展订单号
	optional double feeAmount = 2; //费用总额
	repeated OrderFeeItem feeList = 3; //费用明细
}

//成交结构
message OrderFill
{
	required int32 trdSide = 1; //交易方向, 参见TrdSide的枚举定义
    required uint64 fillID = 2; //成交号
    required string fillIDEx = 3; //扩展成交号(仅查问题时备用)
    optional uint64 orderID = 4; //订单号
    optional string orderIDEx = 5; //扩展订单号(仅查问题时备用)
    required string code = 6; //代码
    required string name = 7; //名称
    required double qty = 8; //成交数量，2位精度，期权单位是"张"
    required double price = 9; //成交价格，3位精度
    required string createTime = 10; //创建时间（成交时间），严格按YYYY-MM-DD HH:MM:SS或YYYY-MM-DD HH:MM:SS.MS格式传
    optional int32 counterBrokerID = 11; //对手经纪号，港股有效
    optional string counterBrokerName = 12; //对手经纪名称，港股有效
    optional int32 secMarket = 13; //证券所属市场，参见TrdSecMarket的枚举定义
    optional double createTimestamp = 14; //创建时间戳
    optional double updateTimestamp = 15; //最后更新时间戳
    optional int32 status = 16; //成交状态, 参见OrderFillStatus的枚举定义
    optional int32 trdMarket = 17;  //交易市场, 参见TrdMarket的枚举定义
}

//最大可交易数量
message MaxTrdQtys
{
	//因目前服务器实现的问题，卖空需要先卖掉持仓才能再卖空，是分开两步卖的，买回来同样是逆向两步；而看多的买是可以现金加融资一起一步买的，请注意这个差异
	required double maxCashBuy = 1;             //不使用融资，仅自己的现金最大可买整手股数，期货此字段值为0
    optional double maxCashAndMarginBuy = 2;    //使用融资，自己的现金 + 融资资金总共的最大可买整手股数，期货不适用
    required double maxPositionSell = 3;        //不使用融券(卖空)，仅自己的持仓最大可卖整手股数
    optional double maxSellShort = 4;           //使用融券(卖空)，最大可卖空整手股数，不包括多仓，期货不适用
    optional double maxBuyBack = 5;             //卖空后，需要买回的最大整手股数。因为卖空后，必须先买回已卖空的股数，还掉股票，才能再继续买多。期货不适用
	optional double longRequiredIM = 6;         //开多仓每张合约初始保证金。当前仅期货和期权适用（最低 FutuOpenD 版本要求：5.0.1310）
	optional double shortRequiredIM = 7;        //开空仓每张合约初始保证金。当前仅期货和期权适用（最低 FutuOpenD 版本要求：5.0.1310）
}

//过滤条件，条件组合是"与"不是"或"，用于获取订单、成交、持仓等时二次过滤
message TrdFilterConditions
{
	repeated string codeList = 1; //代码过滤，只返回包含这些代码的数据，没传不过滤
	repeated uint64 idList = 2; //ID主键过滤，只返回包含这些ID的数据，没传不过滤，订单是orderID、成交是fillID、持仓是positionID
	optional string beginTime = 3; //开始时间，严格按YYYY-MM-DD HH:MM:SS或YYYY-MM-DD HH:MM:SS.MS格式传，对持仓无效，拉历史数据必须填
	optional string endTime = 4; //结束时间，严格按YYYY-MM-DD HH:MM:SS或YYYY-MM-DD HH:MM:SS.MS格式传，对持仓无效，拉历史数据必须填
	repeated string orderIDExList = 5; // 服务器订单ID，可以用来替代orderID，二选一
    optional int32 filterMarket = 6; //指定交易市场, 参见TrdMarket的枚举定义
}
