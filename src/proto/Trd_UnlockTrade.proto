syntax = "proto2";
package Trd_UnlockTrade;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdunlocktrade";

import "Common.proto";

message C2S
{
	required bool unlock = 1; //true解锁交易，false锁定交易
	optional string pwdMD5 = 2; //交易密码的MD5转16进制(全小写)，解锁交易必须要填密码，锁定交易不需要验证密码，可不填
	optional int32 securityFirm = 3; //券商标识，取值见Trd_Common.SecurityFirm
}

message S2C
{
	
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	//以下3个字段每条协议都有，注释说明在InitConnect.proto中
	required int32 retType = 1 [default = -400];
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
