syntax = "proto2";
package Trd_ReconfirmOrder;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdreconfirmorder";

import "Common.proto";
import "Trd_Common.proto";

message C2S
{
	required Common.PacketID packetID = 1; //交易写操作防重放攻击
	required Trd_Common.TrdHeader header = 2; //交易公共参数头
	required uint64 orderID = 3; //订单号
	required int32 reconfirmReason = 4; //需要再次确认订单的原因，参见Trd_Common.ReconfirmOrderReason的枚举定义
}

message S2C
{
	required Trd_Common.TrdHeader header = 1; //交易公共参数头
	required uint64 orderID = 2; //订单号
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	//以下3个字段每条协议都有，注释说明在InitConnect.proto中
	required int32 retType = 1 [default = -400];
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
