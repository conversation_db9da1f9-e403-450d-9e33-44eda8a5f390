syntax = "proto2";
package Qot_GetCapitalFlow;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetcapitalflow";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required Qot_Common.Security security = 1; //股票
	optional int32 periodType = 2; // Qot_Common.PeriodType 周期类型
	optional string beginTime = 3; // 开始时间（格式：yyyy-MM-dd），仅周期类型不为实时有效
	optional string endTime = 4; // 结束时间（格式：yyyy-MM-dd），仅周期类型不为实时有效
}

message CapitalFlowItem
{
	required double inFlow = 1; // 整体净流入
	optional string time = 2; //开始时间字符串,以分钟为单位
	optional double timestamp = 3; //开始时间戳
	optional double mainInFlow = 4; // 主力大单净流入，仅周期类型不为实时有效
	optional double superInFlow = 5; // 特大单净流入
	optional double bigInFlow = 6; // 大单净流入
	optional double midInFlow = 7; // 中单净流入
	optional double smlInFlow = 8; // 小单净流入
}

message S2C
{
	repeated CapitalFlowItem flowItemList = 1; //资金流向
	optional string lastValidTime = 2; //数据最后有效时间字符串
	optional double lastValidTimestamp = 3; //数据最后有效时间戳
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
