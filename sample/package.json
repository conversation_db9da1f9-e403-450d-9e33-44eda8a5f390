{"name": "ftwebsocket", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"axios": "^0.19.0", "babel-plugin-prismjs": "^1.1.1", "core-js": "^2.6.9", "element-ui": "^2.11.1", "js-beautify": "^1.10.2", "oneport": "^1.0.2", "prismjs": "^1.17.1", "vue": "^2.6.10", "vue-highlightjs": "^1.3.3", "vue-router": "^3.0.3", "vuex": "^3.0.1", "futu-api": "^9.3.5308"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.10.0", "@vue/cli-service": "^3.10.0", "sass": "^1.32.0", "sass-loader": "^7.1.0", "vue-template-compiler": "^2.6.10"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}